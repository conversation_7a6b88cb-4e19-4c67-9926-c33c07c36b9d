using UnityEngine;
using Mirror;

/// <summary>
/// Constrains a networked player's transform during helicopter flight sequences.
/// Maintains position relative to spawn point and limits rotation for social interaction.
/// </summary>
public class HelicopterTransformConstraint : NetworkBehaviour
{
    [Header("Position Constraints")]
    [SerializeField] private bool constrainPosition = true;
    [SerializeField] private Vector3 constrainedLocalPosition = Vector3.zero;
    
    [Header("Rotation Constraints")]
    [SerializeField] private bool constrainRotation = true;
    [SerializeField] private bool lockXRotation = true;
    [SerializeField] private bool lockZRotation = true;
    [SerializeField] private bool allowYRotation = true;
    [SerializeField] private float yRotationMin = -90f;
    [SerializeField] private float yRotationMax = 90f;
    
    [Header("Constraint Behavior")]
    [SerializeField] private float constraintSmoothness = 10f;
    [SerializeField] private bool enableVisualFeedback = true;
    
    [Head<PERSON>("Debug")]
    [SerializeField] private bool showDebugInfo = false;

    // Private state
    private Transform spawnPointParent;
    private Vector3 initialLocalPosition;
    private Quaternion initialLocalRotation;
    private float currentYRotation = 0f;
    private bool constraintsActive = false;
    
    // Component references
    private ForestIntroPlayer forestIntroPlayer;
    private ForestPlayer forestPlayer;
    
    // Network synchronization
    [SyncVar(hook = nameof(OnConstraintsActiveChanged))]
    private bool networkConstraintsActive = false;

    public bool ConstraintsActive 
    { 
        get => constraintsActive; 
        private set 
        { 
            if (constraintsActive != value)
            {
                constraintsActive = value;
                if (isServer)
                {
                    networkConstraintsActive = value;
                }
                OnConstraintsActiveChanged(constraintsActive, value);
            }
        } 
    }

    public bool HasValidSpawnPoint => spawnPointParent != null;

    void Start()
    {
        InitializeConstraintSystem();
    }

    private void InitializeConstraintSystem()
    {
        // Store spawn point parent (should be set during spawning)
        spawnPointParent = transform.parent;
        
        if (spawnPointParent != null)
        {
            initialLocalPosition = transform.localPosition;
            initialLocalRotation = transform.localRotation;
            currentYRotation = transform.localEulerAngles.y;
            
            Debug.Log($"HelicopterTransformConstraint: Initialized for {gameObject.name} with spawn point {spawnPointParent.name}");
        }
        else
        {
            Debug.LogWarning($"HelicopterTransformConstraint: No spawn point parent found for {gameObject.name}. Constraints will not function properly.");
        }

        // Get player component references
        forestIntroPlayer = GetComponent<ForestIntroPlayer>();
        forestPlayer = GetComponent<ForestPlayer>();
        
        // Register with constraint manager
        HelicopterConstraintManager.Instance?.RegisterConstrainedPlayer(this);
    }

    void Update()
    {
        if (!isLocalPlayer) return;

        if (constraintsActive)
        {
            EnforceConstraints();
        }

        if (showDebugInfo)
        {
            DisplayDebugInfo();
        }
    }

    private void EnforceConstraints()
    {
        if (spawnPointParent == null) return;

        // Enforce position constraints
        if (constrainPosition)
        {
            Vector3 targetLocalPosition = constrainedLocalPosition;
            if (Vector3.Distance(transform.localPosition, targetLocalPosition) > 0.001f)
            {
                transform.localPosition = Vector3.Lerp(transform.localPosition, targetLocalPosition, 
                    constraintSmoothness * Time.deltaTime);
            }
        }

        // Enforce rotation constraints
        if (constrainRotation)
        {
            Vector3 currentEuler = transform.localEulerAngles;
            Vector3 targetEuler = currentEuler;

            // Lock X rotation (pitch)
            if (lockXRotation)
            {
                targetEuler.x = 0f;
            }

            // Lock Z rotation (roll)
            if (lockZRotation)
            {
                targetEuler.z = 0f;
            }

            // Constrain Y rotation (yaw) within limits
            if (allowYRotation)
            {
                float normalizedY = NormalizeAngle(currentEuler.y);
                targetEuler.y = Mathf.Clamp(normalizedY, yRotationMin, yRotationMax);
            }
            else
            {
                targetEuler.y = 0f;
            }

            // Apply rotation constraints smoothly
            Quaternion targetRotation = Quaternion.Euler(targetEuler);
            if (Quaternion.Angle(transform.localRotation, targetRotation) > 0.1f)
            {
                transform.localRotation = Quaternion.Lerp(transform.localRotation, targetRotation, 
                    constraintSmoothness * Time.deltaTime);
            }
        }
    }

    private float NormalizeAngle(float angle)
    {
        while (angle > 180f) angle -= 360f;
        while (angle < -180f) angle += 360f;
        return angle;
    }

    /// <summary>
    /// Activates helicopter constraints for this player
    /// </summary>
    [Server]
    public void ActivateConstraints()
    {
        if (!isServer) return;
        
        ConstraintsActive = true;
        Debug.Log($"HelicopterTransformConstraint: Activated constraints for {gameObject.name}");
    }

    /// <summary>
    /// Deactivates helicopter constraints for this player
    /// </summary>
    [Server]
    public void DeactivateConstraints()
    {
        if (!isServer) return;
        
        ConstraintsActive = false;
        Debug.Log($"HelicopterTransformConstraint: Deactivated constraints for {gameObject.name}");
    }

    /// <summary>
    /// Checks if the player's Y rotation is within allowed limits
    /// </summary>
    public bool IsYRotationWithinLimits(float yRotation)
    {
        if (!allowYRotation) return false;
        
        float normalizedY = NormalizeAngle(yRotation);
        return normalizedY >= yRotationMin && normalizedY <= yRotationMax;
    }

    /// <summary>
    /// Clamps Y rotation to allowed limits
    /// </summary>
    public float ClampYRotation(float yRotation)
    {
        if (!allowYRotation) return 0f;
        
        float normalizedY = NormalizeAngle(yRotation);
        return Mathf.Clamp(normalizedY, yRotationMin, yRotationMax);
    }

    private void OnConstraintsActiveChanged(bool oldValue, bool newValue)
    {
        constraintsActive = newValue;
        
        if (constraintsActive)
        {
            // Store current state when constraints activate
            if (spawnPointParent != null)
            {
                currentYRotation = transform.localEulerAngles.y;
            }
        }
        
        Debug.Log($"HelicopterTransformConstraint: Constraints {(constraintsActive ? "activated" : "deactivated")} for {gameObject.name}");
    }

    private void DisplayDebugInfo()
    {
        if (spawnPointParent == null) return;

        Vector3 localPos = transform.localPosition;
        Vector3 localRot = transform.localEulerAngles;
        
        Debug.Log($"Constraint Debug - {gameObject.name}: " +
                 $"Active: {constraintsActive}, " +
                 $"LocalPos: {localPos:F3}, " +
                 $"LocalRot: {localRot:F1}, " +
                 $"YInLimits: {IsYRotationWithinLimits(localRot.y)}");
    }

    void OnDestroy()
    {
        // Unregister from constraint manager
        HelicopterConstraintManager.Instance?.UnregisterConstrainedPlayer(this);
    }

    void OnDrawGizmosSelected()
    {
        if (spawnPointParent == null) return;

        // Draw constraint visualization
        Gizmos.color = constraintsActive ? Color.red : Color.yellow;
        Gizmos.matrix = spawnPointParent.localToWorldMatrix;
        
        // Draw position constraint
        if (constrainPosition)
        {
            Gizmos.DrawWireCube(constrainedLocalPosition, Vector3.one * 0.1f);
        }
        
        // Draw rotation limits
        if (constrainRotation && allowYRotation)
        {
            Gizmos.color = Color.blue;
            Vector3 center = constrainedLocalPosition;
            
            // Draw Y rotation arc
            float arcRadius = 0.5f;
            Vector3 minDir = Quaternion.Euler(0, yRotationMin, 0) * Vector3.forward;
            Vector3 maxDir = Quaternion.Euler(0, yRotationMax, 0) * Vector3.forward;
            
            Gizmos.DrawLine(center, center + minDir * arcRadius);
            Gizmos.DrawLine(center, center + maxDir * arcRadius);
        }
    }
}
